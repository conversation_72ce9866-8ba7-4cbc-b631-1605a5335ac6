((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['src/pages/test-interceptor.tsx'],
{ "src/pages/test-interceptor.tsx": function (module, exports, __mako_require__){
/**
 * 响应拦截器测试页面
 * 用于测试和调试请求工具类中的响应拦截器功能
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _request = __mako_require__("src/utils/request.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text, Paragraph } = _antd.Typography;
const TestInterceptorPage = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)({});
    const [results, setResults] = (0, _react.useState)({});
    const setButtonLoading = (key, isLoading)=>{
        setLoading((prev)=>({
                ...prev,
                [key]: isLoading
            }));
    };
    const setResult = (key, result)=>{
        setResults((prev)=>({
                ...prev,
                [key]: result
            }));
    };
    // 测试成功响应
    const testSuccessResponse = async ()=>{
        const key = 'success';
        setButtonLoading(key, true);
        try {
            console.log('🧪 [测试] 发起成功请求...');
            const response = await _request.apiRequest.get('/test/success');
            console.log('🧪 [测试] 成功响应:', response);
            setResult(key, {
                success: true,
                data: response
            });
            _antd.message.success('成功请求测试完成');
        } catch (error) {
            console.error('🧪 [测试] 成功请求失败:', error);
            setResult(key, {
                success: false,
                error: error.message
            });
            _antd.message.error('成功请求测试失败');
        } finally{
            setButtonLoading(key, false);
        }
    };
    // 测试401认证错误
    const test401Error = async ()=>{
        const key = 'auth';
        setButtonLoading(key, true);
        try {
            console.log('🧪 [测试] 发起401认证错误请求...');
            const response = await _request.apiRequest.get('/test/401');
            console.log('🧪 [测试] 意外的成功响应:', response);
            setResult(key, {
                success: true,
                data: response
            });
        } catch (error) {
            console.error('🧪 [测试] 401错误捕获:', error);
            setResult(key, {
                success: false,
                error: error.message
            });
            _antd.message.info('401错误测试完成 - 错误已被正确捕获');
        } finally{
            setButtonLoading(key, false);
        }
    };
    // 测试403权限错误
    const test403Error = async ()=>{
        const key = 'permission';
        setButtonLoading(key, true);
        try {
            console.log('🧪 [测试] 发起403权限错误请求...');
            const response = await _request.apiRequest.get('/test/403');
            console.log('🧪 [测试] 意外的成功响应:', response);
            setResult(key, {
                success: true,
                data: response
            });
        } catch (error) {
            console.error('🧪 [测试] 403错误捕获:', error);
            setResult(key, {
                success: false,
                error: error.message
            });
            _antd.message.info('403错误测试完成 - 错误已被正确捕获');
        } finally{
            setButtonLoading(key, false);
        }
    };
    // 测试其他业务错误
    const testBusinessError = async ()=>{
        const key = 'business';
        setButtonLoading(key, true);
        try {
            console.log('🧪 [测试] 发起业务错误请求...');
            const response = await _request.apiRequest.get('/test/business-error');
            console.log('🧪 [测试] 意外的成功响应:', response);
            setResult(key, {
                success: true,
                data: response
            });
        } catch (error) {
            console.error('🧪 [测试] 业务错误捕获:', error);
            setResult(key, {
                success: false,
                error: error.message
            });
            _antd.message.info('业务错误测试完成 - 错误已被正确捕获');
        } finally{
            setButtonLoading(key, false);
        }
    };
    // 测试网络错误
    const testNetworkError = async ()=>{
        const key = 'network';
        setButtonLoading(key, true);
        try {
            console.log('🧪 [测试] 发起网络错误请求...');
            const response = await _request.apiRequest.get('/test/nonexistent-endpoint');
            console.log('🧪 [测试] 意外的成功响应:', response);
            setResult(key, {
                success: true,
                data: response
            });
        } catch (error) {
            console.error('🧪 [测试] 网络错误捕获:', error);
            setResult(key, {
                success: false,
                error: error.message
            });
            _antd.message.info('网络错误测试完成 - 错误已被正确捕获');
        } finally{
            setButtonLoading(key, false);
        }
    };
    // 测试消息组件
    const testMessageComponent = ()=>{
        // 直接测试 message 组件是否正常工作
        console.log('🧪 [测试] 直接测试 message 组件...');
        // 检查 DOM 中是否有 message 容器
        const checkMessageContainer = ()=>{
            const containers = document.querySelectorAll('[class*="ant-message"]');
            console.log('🔍 [DOM检查] 找到的 message 容器:', containers);
            containers.forEach((container, index)=>{
                console.log(`🔍 [DOM检查] 容器 ${index}:`, container);
                console.log(`🔍 [DOM检查] 容器样式:`, window.getComputedStyle(container));
                console.log(`🔍 [DOM检查] 容器位置:`, container.getBoundingClientRect());
            });
            // 检查所有可能的 Antd 相关容器
            const allAntContainers = document.querySelectorAll('[class*="ant-"]');
            console.log('🔍 [DOM检查] 所有 Antd 容器数量:', allAntContainers.length);
        };
        // 测试各种类型的消息
        console.log('🧪 [测试] 测试 message.success...');
        _antd.message.success('✅ 成功消息测试');
        setTimeout(()=>{
            console.log('🧪 [测试] 测试 message.error...');
            _antd.message.error('❌ 错误消息测试');
            checkMessageContainer();
        }, 1000);
        setTimeout(()=>{
            console.log('🧪 [测试] 测试 message.warning...');
            _antd.message.warning('⚠️ 警告消息测试');
            checkMessageContainer();
        }, 2000);
        setTimeout(()=>{
            console.log('🧪 [测试] 测试 message.info...');
            _antd.message.info('ℹ️ 信息消息测试');
            checkMessageContainer();
        }, 3000);
    };
    // 清除所有结果
    const clearResults = ()=>{
        setResults({});
        console.clear();
        _antd.message.info('测试结果已清除');
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            padding: '24px',
            maxWidth: '1200px',
            margin: '0 auto'
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                level: 2,
                children: "响应拦截器测试页面"
            }, void 0, false, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 167,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                children: "此页面用于测试和调试请求工具类中的响应拦截器功能。 请打开浏览器开发者工具的控制台查看详细的调试信息。"
            }, void 0, false, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 168,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 173,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                level: 3,
                children: "消息组件测试"
            }, void 0, false, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 175,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            onClick: testMessageComponent,
                            children: "测试 Message 组件"
                        }, void 0, false, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 178,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "测试 Ant Design Message 组件是否正常工作"
                        }, void 0, false, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 181,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/test-interceptor.tsx",
                    lineNumber: 177,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 176,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 185,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                level: 3,
                children: "API 响应拦截器测试"
            }, void 0, false, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 187,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                direction: "vertical",
                size: "middle",
                style: {
                    width: '100%'
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        title: "成功响应测试",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        loading: loading.success,
                                        onClick: testSuccessResponse,
                                        children: "测试成功响应"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 191,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "测试正常的API成功响应"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 198,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 190,
                                columnNumber: 11
                            }, this),
                            results.success && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: '12px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "结果: "
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 202,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: results.success.success ? 'success' : 'danger',
                                        children: JSON.stringify(results.success, null, 2)
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 203,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 201,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/test-interceptor.tsx",
                        lineNumber: 189,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        title: "认证错误测试 (401)",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        danger: true,
                                        loading: loading.auth,
                                        onClick: test401Error,
                                        children: "测试401错误"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 212,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "测试认证失败的响应处理"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 219,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 211,
                                columnNumber: 11
                            }, this),
                            results.auth && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: '12px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "结果: "
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 223,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: results.auth.success ? 'success' : 'danger',
                                        children: JSON.stringify(results.auth, null, 2)
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 224,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 222,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/test-interceptor.tsx",
                        lineNumber: 210,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        title: "权限错误测试 (403)",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        danger: true,
                                        loading: loading.permission,
                                        onClick: test403Error,
                                        children: "测试403错误"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 233,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "测试权限不足的响应处理"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 240,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 232,
                                columnNumber: 11
                            }, this),
                            results.permission && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: '12px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "结果: "
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 244,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: results.permission.success ? 'success' : 'danger',
                                        children: JSON.stringify(results.permission, null, 2)
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 245,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 243,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/test-interceptor.tsx",
                        lineNumber: 231,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        title: "业务错误测试",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        danger: true,
                                        loading: loading.business,
                                        onClick: testBusinessError,
                                        children: "测试业务错误"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 254,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "测试其他业务逻辑错误的响应处理"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 261,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 253,
                                columnNumber: 11
                            }, this),
                            results.business && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: '12px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "结果: "
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 265,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: results.business.success ? 'success' : 'danger',
                                        children: JSON.stringify(results.business, null, 2)
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 266,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 264,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/test-interceptor.tsx",
                        lineNumber: 252,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        title: "网络错误测试",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        danger: true,
                                        loading: loading.network,
                                        onClick: testNetworkError,
                                        children: "测试网络错误"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 275,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "测试网络连接错误的响应处理"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 282,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 274,
                                columnNumber: 11
                            }, this),
                            results.network && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: '12px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "结果: "
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 286,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: results.network.success ? 'success' : 'danger',
                                        children: JSON.stringify(results.network, null, 2)
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 287,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 285,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/test-interceptor.tsx",
                        lineNumber: 273,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 188,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 295,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        onClick: clearResults,
                        children: "清除测试结果"
                    }, void 0, false, {
                        fileName: "src/pages/test-interceptor.tsx",
                        lineNumber: 298,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                        type: "secondary",
                        children: "清除所有测试结果和控制台日志"
                    }, void 0, false, {
                        fileName: "src/pages/test-interceptor.tsx",
                        lineNumber: 299,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 297,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/test-interceptor.tsx",
        lineNumber: 166,
        columnNumber: 5
    }, this);
};
_s(TestInterceptorPage, "EoMDvW0kSBIDvEZJIcn4lP0WZU8=");
_c = TestInterceptorPage;
var _default = TestInterceptorPage;
var _c;
$RefreshReg$(_c, "TestInterceptorPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_test-interceptor_tsx-async.js.map