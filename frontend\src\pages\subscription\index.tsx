/**
 * 订阅管理页面
 * 临时页面，用于兼容路由配置
 */

import React from 'react';
import { Card, Typography, Button, Space } from 'antd';
import { history } from '@umijs/max';

const { Title, Paragraph } = Typography;

const SubscriptionPage: React.FC = () => {
  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={2}>订阅管理页面</Title>
        <Paragraph>
          这是一个临时的订阅管理页面，用于兼容路由配置。
        </Paragraph>
        <Space>
          <Button type="primary" onClick={() => history.push('/personal-center')}>
            前往个人中心
          </Button>
          <Button onClick={() => history.push('/dashboard')}>
            返回仪表盘
          </Button>
        </Space>
      </Card>
    </div>
  );
};

export default SubscriptionPage;
