/**
 * 请求工具类
 * 基于 umi-request 封装，支持双阶段认证
 */

import { message } from 'antd';
import { extend } from 'umi-request';
import { history } from '@umijs/max';
import type { ApiResponse } from '@/types/api';

// 创建请求实例
const request = extend({
  prefix: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Token 管理器（单令牌系统）
 *
 * 功能说明：
 * - 统一管理用户认证Token的存储和获取
 * - 使用localStorage进行持久化存储
 * - 支持Token的设置、获取、清除和检查
 *
 * 设计理念：
 * - 采用单令牌系统，简化认证流程
 * - Token同时用于用户认证和团队访问
 * - 提供静态方法，便于全局调用
 */
class TokenManager {
  private static readonly TOKEN_KEY = 'auth_token';

  /**
   * 获取当前Token
   */
  static getToken(): string | null {
    return localStorage.getItem(TokenManager.TOKEN_KEY);
  }

  /**
   * 设置Token
   */
  static setToken(token: string): void {
    localStorage.setItem(TokenManager.TOKEN_KEY, token);
  }

  /**
   * 清除Token
   */
  static clearToken(): void {
    localStorage.removeItem(TokenManager.TOKEN_KEY);
  }

  /**
   * 检查是否有Token
   */
  static hasToken(): boolean {
    return !!TokenManager.getToken();
  }
}

/**
 * 请求拦截器
 *
 * 功能：
 * - 自动在请求头中添加Authorization Bearer Token
 * - 统一处理认证信息的注入
 * - 支持无Token的公开接口访问
 */
request.interceptors.request.use((url, options) => {
  const token = TokenManager.getToken();

  if (token) {
    // 添加Authorization头部
    const headers = {
      ...options.headers,
      Authorization: `Bearer ${token}`,
    };
    return {
      url,
      options: { ...options, headers },
    };
  }

  return { url, options };
});

/**
 * 响应拦截器
 *
 * 功能：
 * - 统一处理API响应格式
 * - 自动处理认证失效情况
 * - 统一的错误消息提示
 * - 自动跳转到登录页面
 * - 支持多种错误显示方式
 */
request.interceptors.response.use(
  async (response) => {
    const data = await response.clone().json();

    // 检查业务状态码
    if (data.code !== 200) {
      // 认证失败的处理
      if (data.code === 401) {
        // 检查当前路径，如果是Dashboard相关页面且刚刚进行了团队切换，
        // 可能是Token更新的时序问题，不立即跳转
        const currentPath = window.location.pathname;
        const isDashboardRelated =
          currentPath.startsWith('/dashboard') ||
          currentPath.startsWith('/team');

        // 如果是Dashboard相关页面，延迟处理认证错误
        if (isDashboardRelated) {
          console.warn(
            'Dashboard页面认证失败，可能是Token更新时序问题:',
            data.message,
          );
          return Promise.reject(new Error(data.message));
        }

        // 其他页面立即处理认证错误
        TokenManager.clearToken();
        message.error('登录已过期，请重新登录');
        // 跳转到登录页，避免重复跳转
        if (window.location.pathname !== '/user/login') {
          history.push('/user/login');
        }
        return Promise.reject(new Error(data.message));
      }

      // 处理权限错误
      if (data.code === 403) {
        // 显示后端返回的具体错误消息
        message.error(data.message || '没有权限访问该资源');
        return Promise.reject(new Error(data.message));
      }

      // 其他业务错误，显示错误消息
      message.error(data.message || '请求失败');
      return Promise.reject(new Error(data.message));
    }

    return response;
  },
  (error: any) => {
    // 网络错误或其他错误
    if (error.response) {
      const { status } = error.response;
      if (status === 401) {
        // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题
        const currentPath = window.location.pathname;
        const isDashboardRelated =
          currentPath.startsWith('/dashboard') ||
          currentPath.startsWith('/team');

        if (isDashboardRelated) {
          console.warn('Dashboard页面认证失败，可能是Token更新时序问题');
          // 不立即清除Token和跳转，让页面自己处理
          return Promise.reject(error);
        }

        // 其他页面立即处理认证错误
        TokenManager.clearToken();
        message.error('登录已过期，请重新登录');
        if (window.location.pathname !== '/user/login') {
          history.push('/user/login');
        }
      } else if (status === 403) {
        // 检查是否是团队访问被拒绝的特殊错误
        const errorMessage = error.response?.data?.message;
        if (errorMessage?.includes('停用') || errorMessage?.includes('禁用') || errorMessage?.includes('不是该团队的成员')) {
          // 团队访问相关的错误，使用后端返回的具体消息
          message.error(errorMessage);
        } else {
          // 其他权限错误
          message.error('没有权限访问该资源');
        }
      } else if (status === 404) {
        message.error('请求的资源不存在');
      } else if (status >= 500) {
        message.error('服务器错误，请稍后重试');
      } else {
        message.error(`请求失败: ${status}`);
      }
    } else if (error.request) {
      // 网络连接错误
      message.error('网络错误，请检查网络连接');
    } else {
      // 其他错误
      message.error('请求失败，请重试');
    }

    return Promise.reject(error);
  },
);

// 封装常用的请求方法
export const apiRequest = {
  get: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.get(url, { params });
  },

  post: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.post(url, { data });
  },

  put: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.put(url, { data });
  },

  delete: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.delete(url, { params });
  },
};

// 导出 Token 管理器
export { TokenManager };

// 导出默认请求实例
export default request;
