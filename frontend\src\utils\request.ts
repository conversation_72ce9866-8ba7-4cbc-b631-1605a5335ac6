/**
 * 请求工具类
 * 基于 umi-request 封装，支持双阶段认证
 */

import { message } from 'antd';
import { extend } from 'umi-request';
import { history } from '@umijs/max';
import type { ApiResponse } from '@/types/api';

// 错误处理器声明（在创建请求实例之前定义）
const errorHandler = (error: any) => {
  console.error('❌ [错误处理器] 捕获到错误:', error);

  if (error.response) {
    const { status } = error.response;
    console.error('❌ [错误处理器] HTTP错误状态码:', status);

    // 注意：根据后端规范，正常情况下不应该收到非200的HTTP状态码
    // 但为了兼容性，仍保留这些处理逻辑
    if (status === 401) {
      // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题
      const currentPath = window.location.pathname;
      const isDashboardRelated =
        currentPath.startsWith('/dashboard') ||
        currentPath.startsWith('/team');

      if (isDashboardRelated) {
        console.warn('⚠️ [错误处理器] Dashboard页面HTTP 401错误，可能是Token更新时序问题');
        // 不立即清除Token和跳转，让页面自己处理
        throw error;
      }

      // 其他页面立即处理认证错误
      console.log('🔄 [错误处理器] HTTP 401错误，清除Token并跳转到登录页');
      // TokenManager 在这里还未定义，需要延迟导入
      localStorage.removeItem('auth_token');
      message.error('登录已过期，请重新登录');
      if (window.location.pathname !== '/user/login') {
        // history 在这里还未定义，使用原生方法
        window.location.href = '/user/login';
      }
    } else if (status === 403) {
      // 检查是否是团队访问被拒绝的特殊错误
      const errorMessage = error.response?.data?.message;
      console.log('🚫 [错误处理器] HTTP 403错误:', errorMessage);
      if (errorMessage?.includes('停用') || errorMessage?.includes('禁用') || errorMessage?.includes('不是该团队的成员')) {
        // 团队访问相关的错误，使用后端返回的具体消息
        message.error(errorMessage);
      } else {
        // 其他权限错误
        message.error('没有权限访问该资源');
      }
    } else if (status === 404) {
      console.log('🔍 [错误处理器] HTTP 404错误');
      message.error('请求的资源不存在');
    } else if (status >= 500) {
      console.log('🔥 [错误处理器] HTTP 5xx错误');
      message.error('服务器错误，请稍后重试');
    } else {
      console.log('❓ [错误处理器] 其他HTTP错误:', status);
      message.error(`请求失败: ${status}`);
    }
  } else if (error.request) {
    // 网络连接错误
    console.error('🌐 [错误处理器] 网络连接错误:', error.request);
    message.error('网络错误，请检查网络连接');
  } else {
    // 其他错误
    console.error('❓ [错误处理器] 其他错误:', error.message);
    message.error('请求失败，请重试');
  }

  throw error;
};

// 创建请求实例
const request = extend({
  prefix: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  errorHandler, // 应用错误处理器
});

/**
 * Token 管理器（单令牌系统）
 *
 * 功能说明：
 * - 统一管理用户认证Token的存储和获取
 * - 使用localStorage进行持久化存储
 * - 支持Token的设置、获取、清除和检查
 *
 * 设计理念：
 * - 采用单令牌系统，简化认证流程
 * - Token同时用于用户认证和团队访问
 * - 提供静态方法，便于全局调用
 */
class TokenManager {
  private static readonly TOKEN_KEY = 'auth_token';

  /**
   * 获取当前Token
   */
  static getToken(): string | null {
    return localStorage.getItem(TokenManager.TOKEN_KEY);
  }

  /**
   * 设置Token
   */
  static setToken(token: string): void {
    localStorage.setItem(TokenManager.TOKEN_KEY, token);
  }

  /**
   * 清除Token
   */
  static clearToken(): void {
    localStorage.removeItem(TokenManager.TOKEN_KEY);
  }

  /**
   * 检查是否有Token
   */
  static hasToken(): boolean {
    return !!TokenManager.getToken();
  }
}

/**
 * 请求拦截器
 *
 * 功能：
 * - 自动在请求头中添加Authorization Bearer Token
 * - 统一处理认证信息的注入
 * - 支持无Token的公开接口访问
 */
request.interceptors.request.use((url, options) => {
  const token = TokenManager.getToken();

  if (token) {
    // 添加Authorization头部
    const headers = {
      ...options.headers,
      Authorization: `Bearer ${token}`,
    };
    return {
      url,
      options: { ...options, headers },
    };
  }

  return { url, options };
});

/**
 * 响应拦截器
 *
 * 功能：
 * - 统一处理API响应格式
 * - 自动处理认证失效情况
 * - 统一的错误消息提示
 * - 自动跳转到登录页面
 * - 支持多种错误显示方式
 *
 * 重要说明：
 * - 后端所有API都返回HTTP 200状态码
 * - 真正的成功/失败状态通过响应体中的code字段判断
 * - 成功时code=200，失败时code为其他错误状态码
 * - umi-request 的响应拦截器必须返回 Response 对象
 */
request.interceptors.response.use(async (response) => {
  // 添加调试日志
  console.log('🔍 [响应拦截器] 收到响应:', {
    url: response.url,
    status: response.status,
    headers: Object.fromEntries(response.headers.entries()),
  });

  let data: ApiResponse<any>;

  try {
    // 解析响应数据
    data = await response.clone().json();
    console.log('🔍 [响应拦截器] 解析的响应数据:', data);
  } catch (parseError) {
    console.error('❌ [响应拦截器] JSON解析失败:', parseError);
    message.error('响应数据格式错误');
    return Promise.reject(new Error('响应数据格式错误'));
  }

  // 检查业务状态码
  if (data.code !== 200) {
    console.warn('⚠️ [响应拦截器] 业务错误:', {
      code: data.code,
      message: data.message,
      url: response.url,
    });

    // 认证失败的处理
    if (data.code === 401) {
      // 检查当前路径，如果是Dashboard相关页面且刚刚进行了团队切换，
      // 可能是Token更新的时序问题，不立即跳转
      const currentPath = window.location.pathname;
      const isDashboardRelated =
        currentPath.startsWith('/dashboard') ||
        currentPath.startsWith('/team');

      // 如果是Dashboard相关页面，延迟处理认证错误
      if (isDashboardRelated) {
        console.warn(
          '⚠️ [响应拦截器] Dashboard页面认证失败，可能是Token更新时序问题:',
          data.message,
        );
        return Promise.reject(new Error(data.message));
      }

      // 其他页面立即处理认证错误
      console.log('🔄 [响应拦截器] 清除Token并跳转到登录页');
      TokenManager.clearToken();
      message.error('登录已过期，请重新登录');
      // 跳转到登录页，避免重复跳转
      if (window.location.pathname !== '/user/login') {
        history.push('/user/login');
      }
      return Promise.reject(new Error(data.message));
    }

    // 处理权限错误
    if (data.code === 403) {
      console.log('🚫 [响应拦截器] 权限错误:', data.message);
      // 显示后端返回的具体错误消息
      message.error(data.message || '没有权限访问该资源');
      return Promise.reject(new Error(data.message));
    }

    // 其他业务错误，显示错误消息
    console.log('❌ [响应拦截器] 其他业务错误:', data.message);
    message.error(data.message || '请求失败');
    return Promise.reject(new Error(data.message));
  }

  // 成功响应，返回原始 Response 对象
  console.log('✅ [响应拦截器] 请求成功:', {
    url: response.url,
    code: data.code,
    hasData: !!data.data,
  });

  // 重要：umi-request 的响应拦截器必须返回 Response 对象
  // 数据解析会在后续的 parseResponse 阶段进行
  return response;
});



// 封装常用的请求方法
export const apiRequest = {
  get: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.get(url, { params });
  },

  post: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.post(url, { data });
  },

  put: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.put(url, { data });
  },

  delete: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.delete(url, { params });
  },
};

// 导出 Token 管理器
export { TokenManager };

// 导出默认请求实例
export default request;
