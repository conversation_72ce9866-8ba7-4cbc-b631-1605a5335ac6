import type { RequestConfig, RunTimeLayoutConfig } from '@umijs/max';
import { history } from '@umijs/max';
import {
  ErrorBoundary,
  Footer,
} from '@/components';
import UserFloatButton from '@/components/FloatButton';
import { AuthService, UserService, TeamService } from '@/services';
import type { UserProfileResponse, TeamDetailResponse } from '@/types/api';
import { hasTeamInCurrentToken } from '@/utils/tokenUtils';



// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
export async function getInitialState(): Promise<{
  currentUser?: UserProfileResponse;
  currentTeam?: TeamDetailResponse;
  loading?: boolean;
  fetchUserInfo?: () => Promise<UserProfileResponse | undefined>;
  fetchTeamInfo?: () => Promise<TeamDetailResponse | undefined>;
}> {
  const fetchUserInfo = async (): Promise<UserProfileResponse | undefined> => {
    try {
      return await UserService.getUserProfile();
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 如果获取用户信息失败，可能是token过期，清除登录状态
      AuthService.clearToken();
      return undefined;
    }
  };

  const fetchTeamInfo = async (): Promise<TeamDetailResponse | undefined> => {
    try {
      // 只有当Token中包含团队信息时才尝试获取团队详情
      if (!hasTeamInCurrentToken()) {
        return undefined;
      }
      return await TeamService.getCurrentTeamDetail();
    } catch (error) {
      console.error('获取团队信息失败:', error);
      return undefined;
    }
  };



  // 如果不是登录页面，执行
  const { location } = history;
  if (!['/user/login', '/user/register'].includes(location.pathname)) {
    try {
      // 检查登录状态
      if (!AuthService.isLoggedIn()) {
        return {
          fetchUserInfo,
          fetchTeamInfo,
        };
      }

      // 获取用户信息
      const currentUser = await fetchUserInfo();
      if (!currentUser) {
        return {
          fetchUserInfo,
          fetchTeamInfo,
        };
      }

      // 尝试获取团队信息（如果Token中包含团队信息）
      const currentTeam = await fetchTeamInfo();

      return {
        fetchUserInfo,
        fetchTeamInfo,
        currentUser,
        currentTeam,
      };
    } catch (error) {
      console.error('初始化失败:', error);
      return {
        fetchUserInfo,
        fetchTeamInfo,
      };
    }
  }



  return {
    fetchUserInfo,
    fetchTeamInfo,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  return {
    // 水印
    waterMarkProps: {
      content: initialState?.currentUser?.name,
    },
    // 底部版权信息
    footerRender: () => <Footer />,
    // 移除右侧内容渲染
    rightContentRender: false,
    // 页面切换时触发
    onPageChange: () => {
      const { location } = history;
      // 如果没有登录，重定向到 login
      if (
        !initialState?.currentUser &&
        !['/user/login', '/user/register'].includes(location.pathname)
      ) {
        history.push('/user/login');
      }
    },
    // 自定义布局区域的背景颜色、文字颜色等
    bgLayoutImgList: [
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
        left: 85,
        bottom: 100,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
        bottom: -68,
        right: -45,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
        bottom: 0,
        left: 0,
        width: '331px',
      },
    ],
    // 自定义链接
    links: [],
    // 自定义菜单头
    menuHeaderRender: undefined,
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children) => {
      return (
        <ErrorBoundary>
          {children}
          {/* 全局 FloatButton - 在用户登录后显示 */}
          <UserFloatButton />
        </ErrorBoundary>
      );
    },
    // 移除 settings 属性，使用默认配置
  };
};

/**
 * @name request 配置
 * 使用 utils/request.ts 中的统一错误处理机制
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request: RequestConfig = {
  // 使用默认配置，错误处理由 utils/request.ts 统一管理
};

/**
 * @name antd 配置
 * 配置 Ant Design 组件的全局设置
 * @doc https://umijs.org/docs/max/antd#运行时配置
 */
export const antd = {
  // App 配置，用于配置全局组件如 message、notification 等
  appConfig: {
    message: {
      maxCount: 3,
      duration: 4.5,
      top: 24,
    },
    notification: {
      placement: 'topRight',
      maxCount: 5,
    },
  },
};
