/**
 * 团队详情页面
 * 临时页面，用于兼容路由配置
 */

import React from 'react';
import { Card, Typography, Button, Space } from 'antd';
import { history } from '@umijs/max';

const { Title, Paragraph } = Typography;

const TeamDetailPage: React.FC = () => {
  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={2}>团队详情页面</Title>
        <Paragraph>
          这是一个临时的团队详情页面，用于兼容路由配置。
        </Paragraph>
        <Space>
          <Button type="primary" onClick={() => history.push('/team-management')}>
            前往团队管理
          </Button>
          <Button onClick={() => history.push('/team')}>
            返回团队列表
          </Button>
          <Button onClick={() => history.push('/dashboard')}>
            返回仪表盘
          </Button>
        </Space>
      </Card>
    </div>
  );
};

export default TeamDetailPage;
