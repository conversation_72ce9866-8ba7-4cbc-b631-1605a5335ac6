package com.teammanage.controller;

import com.teammanage.common.ApiResponse;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 用于测试前端响应拦截器功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = "*")
public class TestController {

    /**
     * 测试成功响应
     */
    @GetMapping("/success")
    public ApiResponse<Map<String, Object>> testSuccess() {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "这是一个成功的响应");
        data.put("timestamp", System.currentTimeMillis());
        data.put("testType", "success");
        
        return ApiResponse.success("请求成功", data);
    }

    /**
     * 测试401认证错误
     */
    @GetMapping("/401")
    public ApiResponse<Void> test401Error() {
        return ApiResponse.unauthorized("用户未认证，请重新登录");
    }

    /**
     * 测试403权限错误
     */
    @GetMapping("/403")
    public ApiResponse<Void> test403Error() {
        return ApiResponse.forbidden("权限不足，无法访问该资源");
    }

    /**
     * 测试业务错误
     */
    @GetMapping("/business-error")
    public ApiResponse<Void> testBusinessError() {
        return ApiResponse.badRequest("业务逻辑错误：参数验证失败");
    }

    /**
     * 测试服务器错误
     */
    @GetMapping("/server-error")
    public ApiResponse<Void> testServerError() {
        return ApiResponse.error("服务器内部错误");
    }

    /**
     * 测试自定义错误码
     */
    @GetMapping("/custom-error")
    public ApiResponse<Void> testCustomError() {
        return ApiResponse.error(1001, "自定义业务错误：数据不存在");
    }

    /**
     * 测试带数据的成功响应
     */
    @GetMapping("/success-with-data")
    public ApiResponse<Map<String, Object>> testSuccessWithData() {
        Map<String, Object> data = new HashMap<>();
        data.put("id", 12345);
        data.put("name", "测试数据");
        data.put("description", "这是一个包含数据的成功响应");
        data.put("items", new String[]{"item1", "item2", "item3"});
        data.put("metadata", Map.of(
            "version", "1.0.0",
            "environment", "test",
            "debug", true
        ));
        
        return ApiResponse.success(data);
    }

    /**
     * 测试POST请求成功响应
     */
    @PostMapping("/post-success")
    public ApiResponse<Map<String, Object>> testPostSuccess(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "POST请求处理成功");
        data.put("receivedData", requestData);
        data.put("processedAt", System.currentTimeMillis());
        
        return ApiResponse.success("POST请求成功", data);
    }

    /**
     * 测试POST请求业务错误
     */
    @PostMapping("/post-error")
    public ApiResponse<Void> testPostError(@RequestBody Map<String, Object> requestData) {
        // 模拟参数验证失败
        if (!requestData.containsKey("required_field")) {
            return ApiResponse.badRequest("缺少必需字段：required_field");
        }
        
        return ApiResponse.success();
    }

    /**
     * 测试团队访问被拒绝错误（特殊的403错误）
     */
    @GetMapping("/team-access-denied")
    public ApiResponse<Void> testTeamAccessDenied() {
        return ApiResponse.forbidden("用户不是该团队的成员，访问被拒绝");
    }

    /**
     * 测试团队被停用错误（特殊的403错误）
     */
    @GetMapping("/team-disabled")
    public ApiResponse<Void> testTeamDisabled() {
        return ApiResponse.forbidden("该团队已被停用，无法访问");
    }

    /**
     * 测试延迟响应（用于测试超时处理）
     */
    @GetMapping("/delayed")
    public ApiResponse<Map<String, Object>> testDelayedResponse(@RequestParam(defaultValue = "1000") long delay) {
        try {
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return ApiResponse.error("请求被中断");
        }
        
        Map<String, Object> data = new HashMap<>();
        data.put("message", "延迟响应完成");
        data.put("delay", delay);
        data.put("timestamp", System.currentTimeMillis());
        
        return ApiResponse.success("延迟响应成功", data);
    }

    /**
     * 测试大数据响应
     */
    @GetMapping("/large-data")
    public ApiResponse<Map<String, Object>> testLargeDataResponse() {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "大数据响应测试");
        
        // 生成大量测试数据
        Map<String, Object> largeData = new HashMap<>();
        for (int i = 0; i < 1000; i++) {
            largeData.put("item_" + i, "这是第" + i + "个测试数据项，包含一些文本内容用于测试大数据响应处理能力");
        }
        data.put("largeData", largeData);
        data.put("itemCount", 1000);
        
        return ApiResponse.success("大数据响应成功", data);
    }

    /**
     * 获取测试接口列表
     */
    @GetMapping("/endpoints")
    public ApiResponse<Map<String, Object>> getTestEndpoints() {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "测试接口列表");
        data.put("endpoints", new String[]{
            "GET /api/test/success - 测试成功响应",
            "GET /api/test/401 - 测试401认证错误",
            "GET /api/test/403 - 测试403权限错误",
            "GET /api/test/business-error - 测试业务错误",
            "GET /api/test/server-error - 测试服务器错误",
            "GET /api/test/custom-error - 测试自定义错误码",
            "GET /api/test/success-with-data - 测试带数据的成功响应",
            "POST /api/test/post-success - 测试POST请求成功响应",
            "POST /api/test/post-error - 测试POST请求业务错误",
            "GET /api/test/team-access-denied - 测试团队访问被拒绝错误",
            "GET /api/test/team-disabled - 测试团队被停用错误",
            "GET /api/test/delayed?delay=1000 - 测试延迟响应",
            "GET /api/test/large-data - 测试大数据响应",
            "GET /api/test/endpoints - 获取测试接口列表"
        });
        
        return ApiResponse.success(data);
    }
}
