/**
 * 响应拦截器测试页面
 * 用于测试和调试请求工具类中的响应拦截器功能
 */

import React, { useState } from 'react';
import { Button, Card, Space, Typography, Divider, message } from 'antd';
import { apiRequest } from '@/utils/request';

const { Title, Text, Paragraph } = Typography;

const TestInterceptorPage: React.FC = () => {
  const [loading, setLoading] = useState<{ [key: string]: boolean }>({});
  const [results, setResults] = useState<{ [key: string]: any }>({});

  const setButtonLoading = (key: string, isLoading: boolean) => {
    setLoading(prev => ({ ...prev, [key]: isLoading }));
  };

  const setResult = (key: string, result: any) => {
    setResults(prev => ({ ...prev, [key]: result }));
  };

  // 测试成功响应
  const testSuccessResponse = async () => {
    const key = 'success';
    setButtonLoading(key, true);
    try {
      console.log('🧪 [测试] 发起成功请求...');
      const response = await apiRequest.get('/test/success');
      console.log('🧪 [测试] 成功响应:', response);
      setResult(key, { success: true, data: response });
      message.success('成功请求测试完成');
    } catch (error) {
      console.error('🧪 [测试] 成功请求失败:', error);
      setResult(key, { success: false, error: error.message });
      message.error('成功请求测试失败');
    } finally {
      setButtonLoading(key, false);
    }
  };

  // 测试401认证错误
  const test401Error = async () => {
    const key = 'auth';
    setButtonLoading(key, true);
    try {
      console.log('🧪 [测试] 发起401认证错误请求...');
      const response = await apiRequest.get('/test/401');
      console.log('🧪 [测试] 意外的成功响应:', response);
      setResult(key, { success: true, data: response });
    } catch (error) {
      console.error('🧪 [测试] 401错误捕获:', error);
      setResult(key, { success: false, error: error.message });
      message.info('401错误测试完成 - 错误已被正确捕获');
    } finally {
      setButtonLoading(key, false);
    }
  };

  // 测试403权限错误
  const test403Error = async () => {
    const key = 'permission';
    setButtonLoading(key, true);
    try {
      console.log('🧪 [测试] 发起403权限错误请求...');
      const response = await apiRequest.get('/test/403');
      console.log('🧪 [测试] 意外的成功响应:', response);
      setResult(key, { success: true, data: response });
    } catch (error) {
      console.error('🧪 [测试] 403错误捕获:', error);
      setResult(key, { success: false, error: error.message });
      message.info('403错误测试完成 - 错误已被正确捕获');
    } finally {
      setButtonLoading(key, false);
    }
  };

  // 测试其他业务错误
  const testBusinessError = async () => {
    const key = 'business';
    setButtonLoading(key, true);
    try {
      console.log('🧪 [测试] 发起业务错误请求...');
      const response = await apiRequest.get('/test/business-error');
      console.log('🧪 [测试] 意外的成功响应:', response);
      setResult(key, { success: true, data: response });
    } catch (error) {
      console.error('🧪 [测试] 业务错误捕获:', error);
      setResult(key, { success: false, error: error.message });
      message.info('业务错误测试完成 - 错误已被正确捕获');
    } finally {
      setButtonLoading(key, false);
    }
  };

  // 测试网络错误
  const testNetworkError = async () => {
    const key = 'network';
    setButtonLoading(key, true);
    try {
      console.log('🧪 [测试] 发起网络错误请求...');
      const response = await apiRequest.get('/test/nonexistent-endpoint');
      console.log('🧪 [测试] 意外的成功响应:', response);
      setResult(key, { success: true, data: response });
    } catch (error) {
      console.error('🧪 [测试] 网络错误捕获:', error);
      setResult(key, { success: false, error: error.message });
      message.info('网络错误测试完成 - 错误已被正确捕获');
    } finally {
      setButtonLoading(key, false);
    }
  };

  // 测试消息组件
  const testMessageComponent = () => {
    // 直接测试 message 组件是否正常工作
    console.log('🧪 [测试] 直接测试 message 组件...');
    
    // 检查 DOM 中是否有 message 容器
    const checkMessageContainer = () => {
      const containers = document.querySelectorAll('[class*="ant-message"]');
      console.log('🔍 [DOM检查] 找到的 message 容器:', containers);
      containers.forEach((container, index) => {
        console.log(`🔍 [DOM检查] 容器 ${index}:`, container);
        console.log(`🔍 [DOM检查] 容器样式:`, window.getComputedStyle(container));
        console.log(`🔍 [DOM检查] 容器位置:`, container.getBoundingClientRect());
      });
      
      // 检查所有可能的 Antd 相关容器
      const allAntContainers = document.querySelectorAll('[class*="ant-"]');
      console.log('🔍 [DOM检查] 所有 Antd 容器数量:', allAntContainers.length);
    };

    // 测试各种类型的消息
    console.log('🧪 [测试] 测试 message.success...');
    message.success('✅ 成功消息测试');
    
    setTimeout(() => {
      console.log('🧪 [测试] 测试 message.error...');
      message.error('❌ 错误消息测试');
      checkMessageContainer();
    }, 1000);
    
    setTimeout(() => {
      console.log('🧪 [测试] 测试 message.warning...');
      message.warning('⚠️ 警告消息测试');
      checkMessageContainer();
    }, 2000);
    
    setTimeout(() => {
      console.log('🧪 [测试] 测试 message.info...');
      message.info('ℹ️ 信息消息测试');
      checkMessageContainer();
    }, 3000);
  };

  // 清除所有结果
  const clearResults = () => {
    setResults({});
    console.clear();
    message.info('测试结果已清除');
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>响应拦截器测试页面</Title>
      <Paragraph>
        此页面用于测试和调试请求工具类中的响应拦截器功能。
        请打开浏览器开发者工具的控制台查看详细的调试信息。
      </Paragraph>

      <Divider />

      <Title level={3}>消息组件测试</Title>
      <Card>
        <Space>
          <Button onClick={testMessageComponent}>
            测试 Message 组件
          </Button>
          <Text type="secondary">测试 Ant Design Message 组件是否正常工作</Text>
        </Space>
      </Card>

      <Divider />

      <Title level={3}>API 响应拦截器测试</Title>
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        <Card title="成功响应测试">
          <Space>
            <Button 
              type="primary" 
              loading={loading.success}
              onClick={testSuccessResponse}
            >
              测试成功响应
            </Button>
            <Text type="secondary">测试正常的API成功响应</Text>
          </Space>
          {results.success && (
            <div style={{ marginTop: '12px' }}>
              <Text strong>结果: </Text>
              <Text type={results.success.success ? 'success' : 'danger'}>
                {JSON.stringify(results.success, null, 2)}
              </Text>
            </div>
          )}
        </Card>

        <Card title="认证错误测试 (401)">
          <Space>
            <Button 
              danger
              loading={loading.auth}
              onClick={test401Error}
            >
              测试401错误
            </Button>
            <Text type="secondary">测试认证失败的响应处理</Text>
          </Space>
          {results.auth && (
            <div style={{ marginTop: '12px' }}>
              <Text strong>结果: </Text>
              <Text type={results.auth.success ? 'success' : 'danger'}>
                {JSON.stringify(results.auth, null, 2)}
              </Text>
            </div>
          )}
        </Card>

        <Card title="权限错误测试 (403)">
          <Space>
            <Button 
              danger
              loading={loading.permission}
              onClick={test403Error}
            >
              测试403错误
            </Button>
            <Text type="secondary">测试权限不足的响应处理</Text>
          </Space>
          {results.permission && (
            <div style={{ marginTop: '12px' }}>
              <Text strong>结果: </Text>
              <Text type={results.permission.success ? 'success' : 'danger'}>
                {JSON.stringify(results.permission, null, 2)}
              </Text>
            </div>
          )}
        </Card>

        <Card title="业务错误测试">
          <Space>
            <Button 
              danger
              loading={loading.business}
              onClick={testBusinessError}
            >
              测试业务错误
            </Button>
            <Text type="secondary">测试其他业务逻辑错误的响应处理</Text>
          </Space>
          {results.business && (
            <div style={{ marginTop: '12px' }}>
              <Text strong>结果: </Text>
              <Text type={results.business.success ? 'success' : 'danger'}>
                {JSON.stringify(results.business, null, 2)}
              </Text>
            </div>
          )}
        </Card>

        <Card title="网络错误测试">
          <Space>
            <Button 
              danger
              loading={loading.network}
              onClick={testNetworkError}
            >
              测试网络错误
            </Button>
            <Text type="secondary">测试网络连接错误的响应处理</Text>
          </Space>
          {results.network && (
            <div style={{ marginTop: '12px' }}>
              <Text strong>结果: </Text>
              <Text type={results.network.success ? 'success' : 'danger'}>
                {JSON.stringify(results.network, null, 2)}
              </Text>
            </div>
          )}
        </Card>
      </Space>

      <Divider />

      <Space>
        <Button onClick={clearResults}>清除测试结果</Button>
        <Text type="secondary">清除所有测试结果和控制台日志</Text>
      </Space>
    </div>
  );
};

export default TestInterceptorPage;
